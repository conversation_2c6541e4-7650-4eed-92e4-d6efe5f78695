# Rebuild Conda environment for Forex RL Training

$envName = "forex-rl"
$envFile = "environment.yml"

Write-Host "🔄 Removing environment: $envName (if exists)..."
conda env remove -n $envName -y | Out-Null

Write-Host "📦 Creating environment from $envFile..."
conda env create -f $envFile -y

Write-Host "✅ Environment '$envName' created."

Start-Sleep -Seconds 2

# Check environment registration
$envList = conda env list | Out-String
$exists = $false
if ($envList -match $envName) {
    $exists = $true
}

if ($exists -eq $true) {
    Write-Host "🔍 Checking installed versions:"
    conda run -n $envName python -c "import gym; print('✅ gym version:', gym.__version__)"
    conda run -n $envName python -c "import stable_baselines3 as sb3; print('✅ SB3 version:', sb3.__version__)"
}
else {
    Write-Host "❌ Environment '$envName' was not found after creation."
}

Write-Host ""
Write-Host "👉 To start working, run:"
Write-Host "    conda activate $envName"
Write-Host "    python train/train_ppo.py"
