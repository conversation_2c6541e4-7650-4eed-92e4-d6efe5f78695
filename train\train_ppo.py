import os
import sys

# 🔧 Add current folder to sys.path to fix local imports
sys.path.append(os.path.dirname(__file__))

import torch
from stable_baselines3 import PPO
from stable_baselines3.common.env_checker import check_env
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common.vec_env import DummyVecEnv
from forex_env import ForexEnv

# Auto GPU/CPU selection
device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
print(f"✅ Training on device: {device}")

# Create environment
env = DummyVecEnv([lambda: Monitor(ForexEnv())])
check_env(ForexEnv())

# Create PPO model with tensorboard logging
model = PPO("MlpPolicy", env, verbose=1,
            tensorboard_log="./logs/",
            device=device)

# Train with TensorBoard log tag
model.learn(total_timesteps=100_000, tb_log_name="run_001")

# Save model
model.save("ppo_forex_model")
print("✅ Model saved to: ppo_forex_model.zip")
